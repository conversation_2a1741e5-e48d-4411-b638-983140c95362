# MELONA ORDER BLOCK STRATEGY - MetaTrader 5 Expert Advisor

## Overview
This Expert Advisor (EA) is a complete conversion of the TradingView Pine Script "MELONA ORDER BLOCK STRATEGY (By 3Dots)" for MetaTrader 5. It implements a sophisticated trading strategy that combines Order Block detection, Hull Moving Averages, ATR-based trailing stops, and Heikin Ashi candles.

## Key Features

### 🎯 Core Strategy Components
- **Order Block Detection**: Identifies bullish and bearish order blocks from swing highs/lows
- **SCOB Detection**: Single Candle Order Block detection with engulfing patterns
- **Hull Moving Averages**: Three types - HMA, THMA, EHMA
- **ATR-based Trailing Stops**: Dynamic trailing stop system
- **Heikin Ashi Integration**: Optional Heikin Ashi candle analysis
- **Multi-TP System**: Three take-profit levels (TP1, TP2, TP3)

### 📊 Market Filters
- **Supertrend Filter**: Trend direction confirmation
- **ADX Filter**: Ranging market detection and avoidance
- **Volatility Filter**: ATR-based volatility filtering
- **HMA Trend Filter**: Hull MA direction confirmation

### 🎨 Visual Elements
- Order block rectangles on chart
- Hull MA, Trailing Stop, and Supertrend lines
- Real-time signal information panel
- Entry/exit markers and labels

## Installation Instructions

1. **Copy the EA file**: Place `MelonaOrderBlockEA.mq5` in your MetaTrader 5 `MQL5/Experts/` folder
2. **Compile**: Open MetaEditor and compile the EA (F7)
3. **Attach to Chart**: Drag the EA onto your desired chart
4. **Configure Settings**: Adjust input parameters as needed
5. **Enable Auto Trading**: Ensure auto trading is enabled in MT5

## Input Parameters

### Hull Moving Average Settings
- **HullPrice**: Applied price for Hull MA calculation (default: PRICE_CLOSE)
- **HullLength**: Period for Hull MA (default: 21)
- **HullType**: Type of Hull MA - HMA/THMA/EHMA (default: HULL_HMA)

### ATR & Trailing Stop Settings
- **ATRPeriod**: ATR calculation period (default: 14)
- **KeyValue**: ATR multiplier for trailing stop (default: 2.0)
- **UseATRFilter**: Enable ATR volatility filtering (default: true)

### Take Profit & Stop Loss
- **TP1_Percent**: First take profit percentage (default: 0.5%)
- **TP2_Percent**: Second take profit percentage (default: 1.0%)
- **TP3_Percent**: Third take profit percentage (default: 1.5%)
- **SL_ATR_Multi**: Stop loss ATR multiplier (default: 1.5)

### Order Block Settings
- **ShowOrderBlocks**: Display order blocks on chart (default: true)
- **OB_LookBack**: Order block detection lookback period (default: 20)
- **OB_ATR_Filter**: Order block ATR filter multiplier (default: 1.0)

### Market Filters
- **UseHeikinAshi**: Use Heikin Ashi candles for signals (default: true)
- **EnableFilters**: Enable market condition filters (default: true)
- **UseSupertrend**: Use Supertrend direction filter (default: true)
- **SupertrendPeriod**: Supertrend calculation period (default: 10)
- **SupertrendMulti**: Supertrend ATR multiplier (default: 3.0)
- **UseADXFilter**: Use ADX ranging market filter (default: true)
- **ADXPeriod**: ADX calculation period (default: 14)
- **ADXThreshold**: ADX threshold for trending market (default: 25.0)

### Risk Management
- **LotSize**: Fixed lot size (default: 0.1)
- **MaxTrades**: Maximum concurrent trades (default: 1)
- **UseRiskPercent**: Use percentage-based position sizing (default: false)
- **RiskPercent**: Risk percentage of account balance (default: 2.0%)

### Visual & Alerts
- **DrawLines**: Draw indicator lines and labels (default: true)
- **ShowAlerts**: Show popup alerts for signals (default: true)
- **SendNotifications**: Send push notifications (default: false)

## Trading Logic

### Entry Conditions

#### Buy Signal
1. Heikin Ashi close > trailing stop
2. Price crosses above trailing stop (crossover)
3. Current price > Hull Moving Average
4. All enabled filters pass (Supertrend bullish, ADX > threshold)

#### Sell Signal
1. Heikin Ashi close < trailing stop
2. Price crosses below trailing stop (crossover)
3. Current price < Hull Moving Average
4. All enabled filters pass (Supertrend bearish, ADX > threshold)

### Order Block Detection
- **Bullish Order Blocks**: Identified from bullish candles with higher closes and lower lows
- **Bearish Order Blocks**: Identified from bearish candles with lower closes and higher highs
- **SCOB Detection**: Single candle engulfing patterns with volume confirmation
- **ATR Filtering**: Order blocks must meet minimum ATR-based size requirements

### Trade Management
- **Multiple Take Profits**: Positions split into 3 parts for TP1, TP2, TP3
- **Dynamic Stop Loss**: ATR-based stop loss that trails with price
- **Position Sizing**: Fixed lot or percentage-based risk management
- **Maximum Positions**: Configurable limit on concurrent trades

## Recommended Settings

### For Scalping (M1, M5)
- Hull Length: 14
- ATR Period: 10
- Key Value: 1.5
- TP1/TP2/TP3: 0.3%, 0.6%, 1.0%

### For Day Trading (M15, H1)
- Hull Length: 21
- ATR Period: 14
- Key Value: 2.0
- TP1/TP2/TP3: 0.5%, 1.0%, 1.5%

### For Swing Trading (H4, D1)
- Hull Length: 34
- ATR Period: 20
- Key Value: 2.5
- TP1/TP2/TP3: 1.0%, 2.0%, 3.0%

## Risk Warnings

⚠️ **Important Risk Disclaimers:**
- This EA is for educational and research purposes
- Past performance does not guarantee future results
- Always test on demo account before live trading
- Use proper risk management and position sizing
- Monitor market conditions and EA performance regularly
- Consider market volatility and spread costs

## Troubleshooting

### Common Issues
1. **No trades opening**: Check if auto trading is enabled and filters are not too restrictive
2. **Compilation errors**: Ensure all required MQL5 libraries are available
3. **Visual elements not showing**: Verify DrawLines parameter is enabled
4. **Signals not matching**: Confirm Heikin Ashi and filter settings

### Performance Optimization
- Test different Hull MA types for your market
- Adjust ATR periods based on market volatility
- Optimize TP/SL ratios for your trading style
- Consider time-based filters for specific sessions

## Support and Customization

This EA provides a solid foundation for the MELONA ORDER BLOCK STRATEGY and can be further customized based on specific trading requirements. The code is well-commented and modular for easy modification.

For additional features or customizations, consider:
- Time-based trading sessions
- News filter integration
- Advanced money management
- Multi-symbol trading
- Enhanced visual indicators

## Compilation Status ✅

**All compilation errors have been fixed!**

### Fixed Issues:
- ✅ ENUM_HULL_TYPE declaration corrected (now uses integer constants)
- ✅ Hull type switch statement replaced with if-else logic
- ✅ Type conversion warnings resolved
- ✅ Function call syntax errors fixed
- ✅ Variable scope issues resolved

### Files Ready for Use:
1. **MelonaOrderBlockEA.mq5** - Full-featured EA (918 lines)
2. **MelonaOrderBlockEA_Simple.mq5** - Simplified version for testing (250 lines)
3. **TestMelonaEA.mq5** - Test script for validation

## Quick Start Guide

### Step 1: Compilation
1. Open MetaEditor in MT5
2. Open `MelonaOrderBlockEA.mq5` or `MelonaOrderBlockEA_Simple.mq5`
3. Press F7 to compile
4. Verify "0 errors, 0 warnings" in the compilation log

### Step 2: Testing
1. Run the test script `TestMelonaEA.mq5` first
2. Attach the EA to a chart (start with the Simple version)
3. Enable auto trading in MT5
4. Monitor the Experts tab for log messages

### Step 3: Configuration
- Start with default settings
- Use the Simple version for initial testing
- Gradually move to the full version with all features

## Version History
- **v1.00**: Initial release with full strategy implementation
- **v1.01**: Fixed all compilation errors, added simplified version
