//+------------------------------------------------------------------+
//|                                        MELONA_OrderBlock_EA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "MELONA ORDER BLOCK STRATEGY - Automated EA"
#property description "Based on Order Block detection, Hull MA, ATR trailing stops"

//--- Include necessary libraries
#include <Trade\Trade.mqh>
#include <Math\Stat\Math.mqh>

//--- Global objects
CTrade trade;

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
//--- Hull Moving Average Settings
input group "=== Hull Moving Average Settings ==="
input ENUM_APPLIED_PRICE InpHullPrice = PRICE_CLOSE;     // Hull MA Applied Price
input int InpHullLength = 21;                            // Hull MA Length
input ENUM_TIMEFRAMES InpHullTimeframe = PERIOD_CURRENT; // Hull MA Timeframe
enum ENUM_HULL_TYPE
{
   HULL_HMA,    // Hull Moving Average (HMA)
   HULL_THMA,   // T3 Hull Moving Average (THMA)  
   HULL_EHMA    // Exponential Hull Moving Average (EHMA)
};
input ENUM_HULL_TYPE InpHullType = HULL_HMA;            // Hull MA Type

//--- ATR and Trailing Stop Settings
input group "=== ATR & Trailing Stop Settings ==="
input int InpATRPeriod = 14;                            // ATR Period
input double InpKeyValue = 2.0;                         // Key Value Multiplier for Trailing Stop
input bool InpUseATRFilter = true;                      // Use ATR Volatility Filter
input int InpATRSMAPeriod = 50;                         // ATR SMA Period for Filter

//--- Take Profit and Stop Loss Settings
input group "=== Take Profit & Stop Loss Settings ==="
input double InpTP1Percent = 0.5;                       // TP1 Percentage (%)
input double InpTP2Percent = 1.0;                       // TP2 Percentage (%)
input double InpTP3Percent = 1.5;                       // TP3 Percentage (%)
input bool InpUseMultipleTP = true;                     // Use Multiple TP Levels
input double InpRiskPercent = 2.0;                      // Risk Percentage per Trade

//--- Heikin Ashi Settings
input group "=== Heikin Ashi Settings ==="
input bool InpUseHeikinAshi = true;                     // Use Heikin Ashi Candles
input ENUM_TIMEFRAMES InpHATimeframe = PERIOD_CURRENT;  // Heikin Ashi Timeframe

//--- Order Block Settings
input group "=== Order Block Settings ==="
input bool InpShowOrderBlocks = true;                   // Show Order Blocks on Chart
input int InpOrderBlockLookback = 20;                   // Order Block Lookback Period
input double InpOrderBlockMinSize = 0.0001;             // Minimum Order Block Size
input int InpMaxOrderBlocks = 10;                       // Maximum Order Blocks to Display

//--- SCOB (Single Candle Order Block) Settings
input group "=== SCOB Settings ==="
input bool InpUseSCOB = true;                           // Use SCOB Detection
input double InpSCOBMinBodyRatio = 0.7;                 // SCOB Minimum Body Ratio
input int InpSCOBConfirmationBars = 2;                  // SCOB Confirmation Bars

//--- Market Filter Settings
input group "=== Market Filter Settings ==="
input bool InpUseSupertrendFilter = true;               // Use Supertrend Filter
input int InpSupertrendPeriod = 10;                     // Supertrend Period
input double InpSupertrendMultiplier = 3.0;             // Supertrend Multiplier
input bool InpUseADXFilter = true;                      // Use ADX Ranging Filter
input int InpADXPeriod = 14;                            // ADX Period
input double InpADXThreshold = 25.0;                    // ADX Threshold (below = ranging)

//--- HMA Trend Filter Settings
input group "=== HMA Trend Filter Settings ==="
input bool InpUseHMATrendFilter = true;                 // Enable HMA Trend Filter
input bool InpHMATrendDirection = true;                 // HMA Trend Direction (true=up, false=down)

//--- Visual and Alert Settings
input group "=== Visual & Alert Settings ==="
input bool InpDrawLines = true;                         // Draw Lines and Labels
input bool InpShowTPSLLevels = true;                    // Show TP/SL Levels on Chart
input bool InpSendAlerts = true;                        // Send Alerts
input bool InpPrintLogs = true;                         // Print Logs to Journal

//--- Trading Settings
input group "=== Trading Settings ==="
input double InpLotSize = 0.01;                         // Fixed Lot Size (0 = auto)
input int InpMagicNumber = 123456;                      // Magic Number
input int InpMaxTradesPerSession = 5;                   // Max Trades per Session
input bool InpUseTimeFilter = false;                    // Use Time Filter
input int InpStartHour = 8;                             // Start Trading Hour
input int InpEndHour = 18;                              // End Trading Hour

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
//--- Handles for indicators
int handleATR;
int handleADX;
int handleSupertrend;

//--- Arrays for calculations
double atrBuffer[];
double adxBuffer[];
double supertrendBuffer[];
double hullBuffer[];
double heikinAshiOpen[], heikinAshiHigh[], heikinAshiLow[], heikinAshiClose[];

//--- Trading variables
double currentTrailingStop = 0.0;
bool trailingStopDirection = true; // true = bullish, false = bearish
int tradesThisSession = 0;
datetime lastTradeTime = 0;

//--- Order Block structures
struct OrderBlock
{
   double high;
   double low;
   datetime time;
   bool isBullish;
   bool isValid;
};

OrderBlock orderBlocks[];

//--- SCOB structures  
struct SCOB
{
   double high;
   double low;
   double open;
   double close;
   datetime time;
   bool isBullish;
   bool isConfirmed;
};

SCOB scobs[];

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Set up trade object
   trade.SetExpertMagicNumber(InpMagicNumber);
   trade.SetDeviationInPoints(10);
   trade.SetTypeFilling(ORDER_FILLING_FOK);
   
   //--- Initialize indicator handles
   handleATR = iATR(_Symbol, _Period, InpATRPeriod);
   if(InpUseADXFilter)
      handleADX = iADX(_Symbol, _Period, InpADXPeriod);
   
   //--- Check handles
   if(handleATR == INVALID_HANDLE)
   {
      Print("Error creating ATR indicator handle");
      return INIT_FAILED;
   }
   
   if(InpUseADXFilter && handleADX == INVALID_HANDLE)
   {
      Print("Error creating ADX indicator handle");
      return INIT_FAILED;
   }
   
   //--- Initialize arrays
   ArraySetAsSeries(atrBuffer, true);
   ArraySetAsSeries(adxBuffer, true);
   ArraySetAsSeries(supertrendBuffer, true);
   ArraySetAsSeries(hullBuffer, true);
   ArraySetAsSeries(heikinAshiOpen, true);
   ArraySetAsSeries(heikinAshiHigh, true);
   ArraySetAsSeries(heikinAshiLow, true);
   ArraySetAsSeries(heikinAshiClose, true);
   
   //--- Initialize order block and SCOB arrays
   ArrayResize(orderBlocks, InpMaxOrderBlocks);
   ArrayResize(scobs, 50); // Keep last 50 SCOBs
   
   //--- Reset session trade counter
   tradesThisSession = 0;
   
   Print("MELONA Order Block EA initialized successfully");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Release indicator handles
   if(handleATR != INVALID_HANDLE)
      IndicatorRelease(handleATR);
   if(handleADX != INVALID_HANDLE)
      IndicatorRelease(handleADX);
   if(handleSupertrend != INVALID_HANDLE)
      IndicatorRelease(handleSupertrend);
      
   //--- Clean up chart objects if any
   ObjectsDeleteAll(0, "MELONA_");
   
   Print("MELONA Order Block EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   //--- Check if new bar
   static datetime lastBarTime = 0;
   datetime currentBarTime = iTime(_Symbol, _Period, 0);
   
   if(currentBarTime == lastBarTime)
      return;
      
   lastBarTime = currentBarTime;
   
   //--- Update indicators and calculations
   if(!UpdateIndicators())
      return;
      
   //--- Calculate Hull Moving Average
   CalculateHullMA();
   
   //--- Calculate Heikin Ashi if enabled
   if(InpUseHeikinAshi)
      CalculateHeikinAshi();
      
   //--- Update trailing stop
   UpdateTrailingStop();
   
   //--- Detect order blocks
   if(InpShowOrderBlocks)
      DetectOrderBlocks();
      
   //--- Detect SCOBs
   if(InpUseSCOB)
      DetectSCOBs();
      
   //--- Check for trading signals
   CheckTradingSignals();
   
   //--- Manage existing positions
   ManagePositions();
}

//+------------------------------------------------------------------+
//| Update all indicators                                            |
//+------------------------------------------------------------------+
bool UpdateIndicators()
{
   //--- Update ATR
   if(CopyBuffer(handleATR, 0, 0, 50, atrBuffer) <= 0)
   {
      Print("Error copying ATR buffer");
      return false;
   }

   //--- Update ADX if enabled
   if(InpUseADXFilter)
   {
      if(CopyBuffer(handleADX, 0, 0, 50, adxBuffer) <= 0)
      {
         Print("Error copying ADX buffer");
         return false;
      }
   }

   return true;
}

//+------------------------------------------------------------------+
//| Calculate Hull Moving Average                                    |
//+------------------------------------------------------------------+
void CalculateHullMA()
{
   ArrayResize(hullBuffer, 50);

   for(int i = 0; i < 50; i++)
   {
      switch(InpHullType)
      {
         case HULL_HMA:
            hullBuffer[i] = CalculateHMA(i, InpHullLength);
            break;
         case HULL_THMA:
            hullBuffer[i] = CalculateTHMA(i, InpHullLength);
            break;
         case HULL_EHMA:
            hullBuffer[i] = CalculateEHMA(i, InpHullLength);
            break;
         default:
            hullBuffer[i] = CalculateHMA(i, InpHullLength);
            break;
      }
   }
}

//+------------------------------------------------------------------+
//| Calculate standard Hull Moving Average                          |
//+------------------------------------------------------------------+
double CalculateHMA(int shift, int period)
{
   if(period <= 0) return 0;

   int half_period = period / 2;
   int sqrt_period = (int)MathSqrt(period);

   double wma_half = CalculateWMA(shift, half_period) * 2;
   double wma_full = CalculateWMA(shift, period);
   double raw_hull = wma_half - wma_full;

   // Smooth with WMA of sqrt(period) - simplified
   return raw_hull;
}

//+------------------------------------------------------------------+
//| Calculate T3 Hull Moving Average                                |
//+------------------------------------------------------------------+
double CalculateTHMA(int shift, int period)
{
   // Simplified T3 implementation
   double ema = CalculateEMA(shift, period);
   return ema;
}

//+------------------------------------------------------------------+
//| Calculate Exponential Hull Moving Average                       |
//+------------------------------------------------------------------+
double CalculateEHMA(int shift, int period)
{
   // Simplified EHMA implementation
   double ema = CalculateEMA(shift, period);
   return ema;
}

//+------------------------------------------------------------------+
//| Calculate Weighted Moving Average                               |
//+------------------------------------------------------------------+
double CalculateWMA(int shift, int period)
{
   double sum = 0;
   double weight_sum = 0;

   for(int i = 0; i < period; i++)
   {
      double price = iClose(_Symbol, _Period, shift + i);
      double weight = period - i;
      sum += price * weight;
      weight_sum += weight;
   }

   return weight_sum > 0 ? sum / weight_sum : 0;
}

//+------------------------------------------------------------------+
//| Calculate Exponential Moving Average                            |
//+------------------------------------------------------------------+
double CalculateEMA(int shift, int period)
{
   double alpha = 2.0 / (period + 1);
   double ema = iClose(_Symbol, _Period, shift + period - 1);

   for(int i = period - 2; i >= 0; i--)
   {
      double price = iClose(_Symbol, _Period, shift + i);
      ema = alpha * price + (1 - alpha) * ema;
   }

   return ema;
}

//+------------------------------------------------------------------+
//| Calculate Heikin Ashi values                                    |
//+------------------------------------------------------------------+
void CalculateHeikinAshi()
{
   ArrayResize(heikinAshiOpen, 50);
   ArrayResize(heikinAshiHigh, 50);
   ArrayResize(heikinAshiLow, 50);
   ArrayResize(heikinAshiClose, 50);

   for(int i = 49; i >= 0; i--)
   {
      double open = iOpen(_Symbol, _Period, i);
      double high = iHigh(_Symbol, _Period, i);
      double low = iLow(_Symbol, _Period, i);
      double close = iClose(_Symbol, _Period, i);

      // HA Close = (O + H + L + C) / 4
      heikinAshiClose[i] = (open + high + low + close) / 4.0;

      // HA Open = (Previous HA Open + Previous HA Close) / 2
      if(i == 49)
         heikinAshiOpen[i] = (open + close) / 2.0;
      else
         heikinAshiOpen[i] = (heikinAshiOpen[i + 1] + heikinAshiClose[i + 1]) / 2.0;

      // HA High = Max(High, HA Open, HA Close)
      heikinAshiHigh[i] = MathMax(high, MathMax(heikinAshiOpen[i], heikinAshiClose[i]));

      // HA Low = Min(Low, HA Open, HA Close)
      heikinAshiLow[i] = MathMin(low, MathMin(heikinAshiOpen[i], heikinAshiClose[i]));
   }
}

//+------------------------------------------------------------------+
//| Update trailing stop                                            |
//+------------------------------------------------------------------+
void UpdateTrailingStop()
{
   if(ArraySize(atrBuffer) == 0) return;

   double hl2 = (iHigh(_Symbol, _Period, 0) + iLow(_Symbol, _Period, 0)) / 2.0;
   double atr_value = atrBuffer[0] * InpKeyValue;

   double upper_band = hl2 + atr_value;
   double lower_band = hl2 - atr_value;

   double close_price = InpUseHeikinAshi ? heikinAshiClose[0] : iClose(_Symbol, _Period, 0);

   if(close_price > upper_band)
   {
      currentTrailingStop = lower_band;
      trailingStopDirection = true; // Bullish
   }
   else if(close_price < lower_band)
   {
      currentTrailingStop = upper_band;
      trailingStopDirection = false; // Bearish
   }
   // Else maintain previous trailing stop
}

//+------------------------------------------------------------------+
//| Detect Order Blocks                                             |
//+------------------------------------------------------------------+
void DetectOrderBlocks()
{
   // Initialize order blocks array
   for(int i = 0; i < InpMaxOrderBlocks; i++)
   {
      orderBlocks[i].isValid = false;
   }

   int blockIndex = 0;

   for(int i = 2; i < InpOrderBlockLookback && blockIndex < InpMaxOrderBlocks; i++)
   {
      double open = iOpen(_Symbol, _Period, i);
      double high = iHigh(_Symbol, _Period, i);
      double low = iLow(_Symbol, _Period, i);
      double close = iClose(_Symbol, _Period, i);

      double prev_high = iHigh(_Symbol, _Period, i + 1);
      double prev_low = iLow(_Symbol, _Period, i + 1);
      double prev_close = iClose(_Symbol, _Period, i + 1);

      double next_close = iClose(_Symbol, _Period, i - 1);

      double candle_size = high - low;
      if(candle_size < InpOrderBlockMinSize) continue;

      // Bullish Order Block
      if(close > open && close > prev_close && low < prev_low && next_close > close)
      {
         orderBlocks[blockIndex].high = high;
         orderBlocks[blockIndex].low = low;
         orderBlocks[blockIndex].time = iTime(_Symbol, _Period, i);
         orderBlocks[blockIndex].isBullish = true;
         orderBlocks[blockIndex].isValid = true;
         blockIndex++;
      }
      // Bearish Order Block
      else if(close < open && close < prev_close && high > prev_high && next_close < close)
      {
         orderBlocks[blockIndex].high = high;
         orderBlocks[blockIndex].low = low;
         orderBlocks[blockIndex].time = iTime(_Symbol, _Period, i);
         orderBlocks[blockIndex].isBullish = false;
         orderBlocks[blockIndex].isValid = true;
         blockIndex++;
      }
   }
}

//+------------------------------------------------------------------+
//| Detect SCOBs (Single Candle Order Blocks)                      |
//+------------------------------------------------------------------+
void DetectSCOBs()
{
   // Reset SCOB array
   for(int i = 0; i < ArraySize(scobs); i++)
   {
      scobs[i].isConfirmed = false;
   }

   int scobIndex = 0;

   for(int i = 1; i < 20 && scobIndex < ArraySize(scobs); i++)
   {
      double open = iOpen(_Symbol, _Period, i);
      double high = iHigh(_Symbol, _Period, i);
      double low = iLow(_Symbol, _Period, i);
      double close = iClose(_Symbol, _Period, i);

      double prev_open = iOpen(_Symbol, _Period, i + 1);
      double prev_close = iClose(_Symbol, _Period, i + 1);

      double body_size = MathAbs(close - open);
      double candle_size = high - low;
      double body_ratio = candle_size > 0 ? body_size / candle_size : 0;

      if(body_ratio < InpSCOBMinBodyRatio) continue;

      // Bullish SCOB (Engulfing)
      if(close > open && close > prev_open && open < prev_close && body_size > MathAbs(prev_close - prev_open) * 1.2)
      {
         scobs[scobIndex].high = high;
         scobs[scobIndex].low = open;
         scobs[scobIndex].open = open;
         scobs[scobIndex].close = close;
         scobs[scobIndex].time = iTime(_Symbol, _Period, i);
         scobs[scobIndex].isBullish = true;
         scobs[scobIndex].isConfirmed = true;
         scobIndex++;
      }
      // Bearish SCOB (Engulfing)
      else if(close < open && close < prev_open && open > prev_close && body_size > MathAbs(prev_open - prev_close) * 1.2)
      {
         scobs[scobIndex].high = open;
         scobs[scobIndex].low = low;
         scobs[scobIndex].open = open;
         scobs[scobIndex].close = close;
         scobs[scobIndex].time = iTime(_Symbol, _Period, i);
         scobs[scobIndex].isBullish = false;
         scobs[scobIndex].isConfirmed = true;
         scobIndex++;
      }
   }
}

//+------------------------------------------------------------------+
//| Check for trading signals                                       |
//+------------------------------------------------------------------+
void CheckTradingSignals()
{
   // Check time filter
   if(InpUseTimeFilter)
   {
      MqlDateTime dt;
      TimeToStruct(TimeCurrent(), dt);
      if(dt.hour < InpStartHour || dt.hour >= InpEndHour)
         return;
   }

   // Check max trades per session
   if(tradesThisSession >= InpMaxTradesPerSession)
      return;

   // Get current values
   double current_price = iClose(_Symbol, _Period, 0);
   double prev_price = iClose(_Symbol, _Period, 1);

   double ha_close = InpUseHeikinAshi ? heikinAshiClose[0] : current_price;
   double prev_ha_close = InpUseHeikinAshi ? heikinAshiClose[1] : prev_price;

   double hull_ma = ArraySize(hullBuffer) > 0 ? hullBuffer[0] : current_price;

   // Check filters
   bool trend_filter = true;
   bool adx_filter = true;

   if(InpUseADXFilter && ArraySize(adxBuffer) > 0)
   {
      adx_filter = (adxBuffer[0] > InpADXThreshold);
   }

   if(InpUseHMATrendFilter)
   {
      trend_filter = InpHMATrendDirection ? (current_price > hull_ma) : (current_price < hull_ma);
   }

   // Buy Signal
   bool buy_signal = false;
   if(ha_close > currentTrailingStop &&
      prev_ha_close <= currentTrailingStop &&
      current_price > hull_ma &&
      trend_filter && adx_filter)
   {
      buy_signal = true;
   }

   // Sell Signal
   bool sell_signal = false;
   if(ha_close < currentTrailingStop &&
      prev_ha_close >= currentTrailingStop &&
      current_price < hull_ma &&
      trend_filter && adx_filter)
   {
      sell_signal = true;
   }

   // Execute trades
   if(buy_signal && GetOpenPositions() == 0)
   {
      OpenBuyTrade();
   }

   if(sell_signal && GetOpenPositions() == 0)
   {
      OpenSellTrade();
   }
}

//+------------------------------------------------------------------+
//| Manage existing positions                                       |
//+------------------------------------------------------------------+
void ManagePositions()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == InpMagicNumber)
      {
         // Update trailing stops or other position management logic here
         // This is a placeholder for position management
      }
   }
}

//+------------------------------------------------------------------+
//| Open Buy Trade                                                  |
//+------------------------------------------------------------------+
void OpenBuyTrade()
{
   double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double atr_value = ArraySize(atrBuffer) > 0 ? atrBuffer[0] : 0.001;

   double stop_loss = entry_price - (atr_value * 2.0);
   double take_profit = entry_price + (entry_price * InpTP1Percent / 100.0);

   double lot_size = CalculateLotSize(entry_price, stop_loss);

   if(trade.Buy(lot_size, _Symbol, entry_price, stop_loss, take_profit, "MELONA_BUY"))
   {
      tradesThisSession++;
      lastTradeTime = TimeCurrent();

      if(InpSendAlerts)
         Alert("MELONA EA: BUY Signal at ", entry_price);

      if(InpPrintLogs)
         Print("Buy trade opened: Entry=", entry_price, " SL=", stop_loss, " TP=", take_profit);
   }
}

//+------------------------------------------------------------------+
//| Open Sell Trade                                                 |
//+------------------------------------------------------------------+
void OpenSellTrade()
{
   double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double atr_value = ArraySize(atrBuffer) > 0 ? atrBuffer[0] : 0.001;

   double stop_loss = entry_price + (atr_value * 2.0);
   double take_profit = entry_price - (entry_price * InpTP1Percent / 100.0);

   double lot_size = CalculateLotSize(entry_price, stop_loss);

   if(trade.Sell(lot_size, _Symbol, entry_price, stop_loss, take_profit, "MELONA_SELL"))
   {
      tradesThisSession++;
      lastTradeTime = TimeCurrent();

      if(InpSendAlerts)
         Alert("MELONA EA: SELL Signal at ", entry_price);

      if(InpPrintLogs)
         Print("Sell trade opened: Entry=", entry_price, " SL=", stop_loss, " TP=", take_profit);
   }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk                                |
//+------------------------------------------------------------------+
double CalculateLotSize(double entry_price, double stop_loss)
{
   if(InpLotSize > 0)
      return InpLotSize;

   double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double risk_amount = account_balance * InpRiskPercent / 100.0;
   double pip_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double stop_distance = MathAbs(entry_price - stop_loss);

   double lot_size = 0.01;
   if(stop_distance > 0 && pip_value > 0)
   {
      lot_size = risk_amount / (stop_distance / SymbolInfoDouble(_Symbol, SYMBOL_POINT) * pip_value);
   }

   // Normalize lot size
   double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

   lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
   lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;

   return lot_size;
}

//+------------------------------------------------------------------+
//| Get number of open positions                                    |
//+------------------------------------------------------------------+
int GetOpenPositions()
{
   int count = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == InpMagicNumber)
         count++;
   }
   return count;
}
