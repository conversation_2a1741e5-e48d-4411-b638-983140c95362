//+------------------------------------------------------------------+
//|                                        MELONA_OrderBlock_EA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "MELONA ORDER BLOCK STRATEGY - Automated EA"
#property description "Based on Order Block detection, Hull MA, ATR trailing stops"

//--- Include necessary libraries
#include <Trade\Trade.mqh>
#include <Math\Stat\Math.mqh>

//--- Global objects
CTrade trade;

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
//--- Hull Moving Average Settings
input group "=== Hull Moving Average Settings ==="
input ENUM_APPLIED_PRICE InpHullPrice = PRICE_CLOSE;     // Hull MA Applied Price
input int InpHullLength = 21;                            // Hull MA Length
input ENUM_TIMEFRAMES InpHullTimeframe = PERIOD_CURRENT; // Hull MA Timeframe
enum ENUM_HULL_TYPE
{
   HULL_HMA,    // Hull Moving Average (HMA)
   HULL_THMA,   // T3 Hull Moving Average (THMA)  
   HULL_EHMA    // Exponential Hull Moving Average (EHMA)
};
input ENUM_HULL_TYPE InpHullType = HULL_HMA;            // Hull MA Type

//--- ATR and Trailing Stop Settings
input group "=== ATR & Trailing Stop Settings ==="
input int InpATRPeriod = 14;                            // ATR Period
input double InpKeyValue = 2.0;                         // Key Value Multiplier for Trailing Stop
input bool InpUseATRFilter = true;                      // Use ATR Volatility Filter
input int InpATRSMAPeriod = 50;                         // ATR SMA Period for Filter

//--- Take Profit and Stop Loss Settings
input group "=== Take Profit & Stop Loss Settings ==="
input double InpTP1Percent = 0.5;                       // TP1 Percentage (%)
input double InpTP2Percent = 1.0;                       // TP2 Percentage (%)
input double InpTP3Percent = 1.5;                       // TP3 Percentage (%)
input bool InpUseMultipleTP = true;                     // Use Multiple TP Levels
input double InpRiskPercent = 2.0;                      // Risk Percentage per Trade

//--- Heikin Ashi Settings
input group "=== Heikin Ashi Settings ==="
input bool InpUseHeikinAshi = true;                     // Use Heikin Ashi Candles
input ENUM_TIMEFRAMES InpHATimeframe = PERIOD_CURRENT;  // Heikin Ashi Timeframe

//--- Order Block Settings
input group "=== Order Block Settings ==="
input bool InpShowOrderBlocks = true;                   // Show Order Blocks on Chart
input int InpOrderBlockLookback = 20;                   // Order Block Lookback Period
input double InpOrderBlockMinSize = 0.0001;             // Minimum Order Block Size
input int InpMaxOrderBlocks = 10;                       // Maximum Order Blocks to Display

//--- SCOB (Single Candle Order Block) Settings
input group "=== SCOB Settings ==="
input bool InpUseSCOB = true;                           // Use SCOB Detection
input double InpSCOBMinBodyRatio = 0.7;                 // SCOB Minimum Body Ratio
input int InpSCOBConfirmationBars = 2;                  // SCOB Confirmation Bars

//--- Market Filter Settings
input group "=== Market Filter Settings ==="
input bool InpUseSupertrendFilter = true;               // Use Supertrend Filter
input int InpSupertrendPeriod = 10;                     // Supertrend Period
input double InpSupertrendMultiplier = 3.0;             // Supertrend Multiplier
input bool InpUseADXFilter = true;                      // Use ADX Ranging Filter
input int InpADXPeriod = 14;                            // ADX Period
input double InpADXThreshold = 25.0;                    // ADX Threshold (below = ranging)

//--- HMA Trend Filter Settings
input group "=== HMA Trend Filter Settings ==="
input bool InpUseHMATrendFilter = true;                 // Enable HMA Trend Filter
input bool InpHMATrendDirection = true;                 // HMA Trend Direction (true=up, false=down)

//--- Visual and Alert Settings
input group "=== Visual & Alert Settings ==="
input bool InpDrawLines = true;                         // Draw Lines and Labels
input bool InpShowTPSLLevels = true;                    // Show TP/SL Levels on Chart
input bool InpSendAlerts = true;                        // Send Alerts
input bool InpPrintLogs = true;                         // Print Logs to Journal

//--- Trading Settings
input group "=== Trading Settings ==="
input double InpLotSize = 0.01;                         // Fixed Lot Size (0 = auto)
input int InpMagicNumber = 123456;                      // Magic Number
input int InpMaxTradesPerSession = 5;                   // Max Trades per Session
input bool InpUseTimeFilter = false;                    // Use Time Filter
input int InpStartHour = 8;                             // Start Trading Hour
input int InpEndHour = 18;                              // End Trading Hour

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
//--- Handles for indicators
int handleATR;
int handleADX;
int handleSupertrend;

//--- Arrays for calculations
double atrBuffer[];
double adxBuffer[];
double supertrendBuffer[];
double hullBuffer[];
double heikinAshiOpen[], heikinAshiHigh[], heikinAshiLow[], heikinAshiClose[];

//--- Trading variables
double currentTrailingStop = 0.0;
bool trailingStopDirection = true; // true = bullish, false = bearish
int tradesThisSession = 0;
datetime lastTradeTime = 0;

//--- Order Block structures
struct OrderBlock
{
   double high;
   double low;
   datetime time;
   bool isBullish;
   bool isValid;
};

OrderBlock orderBlocks[];

//--- SCOB structures  
struct SCOB
{
   double high;
   double low;
   double open;
   double close;
   datetime time;
   bool isBullish;
   bool isConfirmed;
};

SCOB scobs[];

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Set up trade object
   trade.SetExpertMagicNumber(InpMagicNumber);
   trade.SetDeviationInPoints(10);
   trade.SetTypeFilling(ORDER_FILLING_FOK);
   
   //--- Initialize indicator handles
   handleATR = iATR(_Symbol, _Period, InpATRPeriod);
   if(InpUseADXFilter)
      handleADX = iADX(_Symbol, _Period, InpADXPeriod);
   
   //--- Check handles
   if(handleATR == INVALID_HANDLE)
   {
      Print("Error creating ATR indicator handle");
      return INIT_FAILED;
   }
   
   if(InpUseADXFilter && handleADX == INVALID_HANDLE)
   {
      Print("Error creating ADX indicator handle");
      return INIT_FAILED;
   }
   
   //--- Initialize arrays
   ArraySetAsSeries(atrBuffer, true);
   ArraySetAsSeries(adxBuffer, true);
   ArraySetAsSeries(supertrendBuffer, true);
   ArraySetAsSeries(hullBuffer, true);
   ArraySetAsSeries(heikinAshiOpen, true);
   ArraySetAsSeries(heikinAshiHigh, true);
   ArraySetAsSeries(heikinAshiLow, true);
   ArraySetAsSeries(heikinAshiClose, true);
   
   //--- Initialize order block and SCOB arrays
   ArrayResize(orderBlocks, InpMaxOrderBlocks);
   ArrayResize(scobs, 50); // Keep last 50 SCOBs
   
   //--- Reset session trade counter
   tradesThisSession = 0;
   
   Print("MELONA Order Block EA initialized successfully");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Release indicator handles
   if(handleATR != INVALID_HANDLE)
      IndicatorRelease(handleATR);
   if(handleADX != INVALID_HANDLE)
      IndicatorRelease(handleADX);
   if(handleSupertrend != INVALID_HANDLE)
      IndicatorRelease(handleSupertrend);
      
   //--- Clean up chart objects if any
   ObjectsDeleteAll(0, "MELONA_");
   
   Print("MELONA Order Block EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   //--- Check if new bar
   static datetime lastBarTime = 0;
   datetime currentBarTime = iTime(_Symbol, _Period, 0);
   
   if(currentBarTime == lastBarTime)
      return;
      
   lastBarTime = currentBarTime;
   
   //--- Update indicators and calculations
   if(!UpdateIndicators())
      return;
      
   //--- Calculate Hull Moving Average
   CalculateHullMA();
   
   //--- Calculate Heikin Ashi if enabled
   if(InpUseHeikinAshi)
      CalculateHeikinAshi();
      
   //--- Update trailing stop
   UpdateTrailingStop();
   
   //--- Detect order blocks
   if(InpShowOrderBlocks)
      DetectOrderBlocks();
      
   //--- Detect SCOBs
   if(InpUseSCOB)
      DetectSCOBs();
      
   //--- Check for trading signals
   CheckTradingSignals();
   
   //--- Manage existing positions
   ManagePositions();
}
