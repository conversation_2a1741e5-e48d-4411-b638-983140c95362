//+------------------------------------------------------------------+
//|                                           MelonaOrderBlockEA.mq5 |
//|                                  MELONA ORDER BLOCK STRATEGY EA |
//|                                   Converted from TradingView Pine|
//+------------------------------------------------------------------+
#property copyright "MELONA ORDER BLOCK STRATEGY (By 3Dots) - MT5 Conversion"
#property link      ""
#property version   "1.00"
#property strict

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

//--- Global objects
CTrade         trade;
CPositionInfo  position;
COrderInfo     order;

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
input group "=== Hull Moving Average Settings ==="
input ENUM_APPLIED_PRICE HullPrice = PRICE_CLOSE;        // Hull MA Applied Price
input int                HullLength = 21;                // Hull MA Length
input int                HullType = 0;                 // Hull MA Type (0=HMA, 1=THMA, 2=EHMA)

input group "=== ATR & Trailing Stop Settings ==="
input int                ATRPeriod = 14;                // ATR Period
input double             KeyValue = 2.0;                // ATR Key Value Multiplier
input bool               UseATRFilter = true;           // Use ATR Volatility Filter

input group "=== Take Profit & Stop Loss ==="
input double             TP1_Percent = 0.5;             // TP1 Percentage
input double             TP2_Percent = 1.0;             // TP2 Percentage  
input double             TP3_Percent = 1.5;             // TP3 Percentage
input double             SL_ATR_Multi = 1.5;            // Stop Loss ATR Multiplier

input group "=== Order Block Settings ==="
input bool               ShowOrderBlocks = true;        // Show Order Blocks
input int                OB_LookBack = 20;              // Order Block Lookback Period
input double             OB_ATR_Filter = 1.0;           // Order Block ATR Filter

input group "=== Market Filters ==="
input bool               UseHeikinAshi = true;          // Use Heikin Ashi
input bool               EnableFilters = true;          // Enable Market Filters
input bool               UseSupertrend = true;          // Use Supertrend Filter
input int                SupertrendPeriod = 10;         // Supertrend Period
input double             SupertrendMulti = 3.0;         // Supertrend Multiplier
input bool               UseADXFilter = true;           // Use ADX Ranging Filter
input int                ADXPeriod = 14;                // ADX Period
input double             ADXThreshold = 25.0;           // ADX Threshold (below = ranging)

input group "=== Risk Management ==="
input double             LotSize = 0.1;                 // Lot Size
input int                MaxTrades = 1;                 // Max Concurrent Trades
input bool               UseRiskPercent = false;        // Use Risk Percentage
input double             RiskPercent = 2.0;             // Risk Percentage of Account

input group "=== Visual & Alerts ==="
input bool               DrawLines = true;              // Draw Lines and Labels
input bool               ShowAlerts = true;             // Show Alerts
input bool               SendNotifications = false;     // Send Push Notifications

//+------------------------------------------------------------------+
//| Hull Moving Average Types                                        |
//+------------------------------------------------------------------+
#define HULL_HMA  0    // Hull Moving Average
#define HULL_THMA 1    // T3 Hull Moving Average
#define HULL_EHMA 2    // Exponential Hull Moving Average

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
double HullMA[];
double TrailingStop[];
double ATR[];
double Supertrend[];
double ADX[];

// Heikin Ashi values
double HA_Open[];
double HA_High[];
double HA_Low[];
double HA_Close[];

// Order Block arrays
double BullishOB_High[];
double BullishOB_Low[];
double BearishOB_High[];
double BearishOB_Low[];
datetime OB_Time[];

// Signal tracking
bool LastBuySignal = false;
bool LastSellSignal = false;
datetime LastSignalTime = 0;

// Trade management
int MagicNumber = 123456;
string EA_Comment = "MelonaOB_EA";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Set magic number for trades
   trade.SetExpertMagicNumber(MagicNumber);
   
   // Initialize arrays
   ArraySetAsSeries(HullMA, true);
   ArraySetAsSeries(TrailingStop, true);
   ArraySetAsSeries(ATR, true);
   ArraySetAsSeries(Supertrend, true);
   ArraySetAsSeries(ADX, true);
   
   ArraySetAsSeries(HA_Open, true);
   ArraySetAsSeries(HA_High, true);
   ArraySetAsSeries(HA_Low, true);
   ArraySetAsSeries(HA_Close, true);
   
   ArraySetAsSeries(BullishOB_High, true);
   ArraySetAsSeries(BullishOB_Low, true);
   ArraySetAsSeries(BearishOB_High, true);
   ArraySetAsSeries(BearishOB_Low, true);
   ArraySetAsSeries(OB_Time, true);
   
   Print("MELONA ORDER BLOCK EA initialized successfully");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Clean up objects and arrays
   ObjectsDeleteAll(0, "MelonaOB_");
   Print("MELONA ORDER BLOCK EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Check if new bar
   static datetime lastBarTime = 0;
   datetime currentBarTime = iTime(_Symbol, _Period, 0);
   
   if(currentBarTime == lastBarTime)
      return;
   lastBarTime = currentBarTime;
   
   // Calculate indicators
   CalculateIndicators();
   
   // Detect order blocks
   if(ShowOrderBlocks)
      DetectOrderBlocks();
   
   // Check for entry signals
   CheckEntrySignals();
   
   // Manage existing trades
   ManageTrades();
   
   // Update visual elements
   if(DrawLines)
      UpdateVisuals();
}

//+------------------------------------------------------------------+
//| Calculate all indicators                                         |
//+------------------------------------------------------------------+
void CalculateIndicators()
{
   // Calculate ATR
   int atr_handle = iATR(_Symbol, _Period, ATRPeriod);
   if(CopyBuffer(atr_handle, 0, 0, 50, ATR) <= 0)
      return;

   // Calculate Hull Moving Average
   CalculateHullMA();

   // Calculate Heikin Ashi if enabled
   if(UseHeikinAshi)
      CalculateHeikinAshi();

   // Calculate Trailing Stop
   CalculateTrailingStop();

   // Calculate Supertrend if enabled
   if(UseSupertrend)
      CalculateSupertrend();

   // Calculate ADX if enabled
   if(UseADXFilter)
   {
      int adx_handle = iADX(_Symbol, _Period, ADXPeriod);
      if(CopyBuffer(adx_handle, 0, 0, 50, ADX) <= 0)
         return;
   }
}

//+------------------------------------------------------------------+
//| Calculate Hull Moving Average                                    |
//+------------------------------------------------------------------+
void CalculateHullMA()
{
   ArrayResize(HullMA, 50);

   for(int i = 0; i < 50; i++)
   {
      if(HullType == HULL_HMA)
         HullMA[i] = CalculateHMA(i, HullLength);
      else if(HullType == HULL_THMA)
         HullMA[i] = CalculateTHMA(i, HullLength);
      else if(HullType == HULL_EHMA)
         HullMA[i] = CalculateEHMA(i, HullLength);
      else
         HullMA[i] = CalculateHMA(i, HullLength); // Default to HMA
   }
}

//+------------------------------------------------------------------+
//| Calculate Standard Hull Moving Average                          |
//+------------------------------------------------------------------+
double CalculateHMA(int shift, int period)
{
   if(period <= 0) return 0;

   int half_period = period / 2;
   int sqrt_period = (int)MathSqrt(period);

   // WMA of half period * 2
   double wma_half = CalculateWMA(shift, half_period) * 2;

   // WMA of full period
   double wma_full = CalculateWMA(shift, period);

   // Raw Hull value
   double raw_hull = wma_half - wma_full;

   // Smooth with WMA of sqrt(period)
   return CalculateWMAFromArray(shift, sqrt_period, raw_hull);
}

//+------------------------------------------------------------------+
//| Calculate T3 Hull Moving Average                                |
//+------------------------------------------------------------------+
double CalculateTHMA(int shift, int period)
{
   // Simplified T3 Hull implementation
   double ema1 = CalculateEMA(shift, period);
   double ema2 = CalculateEMAFromValue(shift, period, ema1);
   double ema3 = CalculateEMAFromValue(shift, period, ema2);

   double c1 = -0.28;
   double c2 = 1.28;
   double c3 = -0.28;

   return c1 * ema3 + c2 * ema2 + c3 * ema1;
}

//+------------------------------------------------------------------+
//| Calculate Exponential Hull Moving Average                       |
//+------------------------------------------------------------------+
double CalculateEHMA(int shift, int period)
{
   // EHMA implementation using EMA instead of WMA
   int half_period = period / 2;
   int sqrt_period = (int)MathSqrt(period);

   double ema_half = CalculateEMA(shift, half_period) * 2;
   double ema_full = CalculateEMA(shift, period);
   double raw_hull = ema_half - ema_full;

   return CalculateEMAFromValue(shift, sqrt_period, raw_hull);
}

//+------------------------------------------------------------------+
//| Calculate Weighted Moving Average                               |
//+------------------------------------------------------------------+
double CalculateWMA(int shift, int period)
{
   double sum = 0;
   double weight_sum = 0;

   for(int i = 0; i < period; i++)
   {
      double price = iClose(_Symbol, _Period, shift + i);
      double weight = period - i;
      sum += price * weight;
      weight_sum += weight;
   }

   return weight_sum > 0 ? sum / weight_sum : 0;
}

//+------------------------------------------------------------------+
//| Calculate WMA from array values                                 |
//+------------------------------------------------------------------+
double CalculateWMAFromArray(int shift, int period, double value)
{
   // Simplified implementation - in real scenario would use array of values
   return value; // Placeholder
}

//+------------------------------------------------------------------+
//| Calculate Exponential Moving Average                            |
//+------------------------------------------------------------------+
double CalculateEMA(int shift, int period)
{
   double alpha = 2.0 / (period + 1);
   double ema = iClose(_Symbol, _Period, shift + period - 1);

   for(int i = period - 2; i >= 0; i--)
   {
      double price = iClose(_Symbol, _Period, shift + i);
      ema = alpha * price + (1 - alpha) * ema;
   }

   return ema;
}

//+------------------------------------------------------------------+
//| Calculate EMA from specific value                               |
//+------------------------------------------------------------------+
double CalculateEMAFromValue(int shift, int period, double value)
{
   // Simplified implementation
   return value; // Placeholder
}

//+------------------------------------------------------------------+
//| Calculate Heikin Ashi values                                    |
//+------------------------------------------------------------------+
void CalculateHeikinAshi()
{
   ArrayResize(HA_Open, 50);
   ArrayResize(HA_High, 50);
   ArrayResize(HA_Low, 50);
   ArrayResize(HA_Close, 50);

   for(int i = 49; i >= 0; i--)
   {
      double open = iOpen(_Symbol, _Period, i);
      double high = iHigh(_Symbol, _Period, i);
      double low = iLow(_Symbol, _Period, i);
      double close = iClose(_Symbol, _Period, i);

      // HA Close = (O + H + L + C) / 4
      HA_Close[i] = (open + high + low + close) / 4.0;

      // HA Open = (Previous HA Open + Previous HA Close) / 2
      if(i == 49)
         HA_Open[i] = (open + close) / 2.0;
      else
         HA_Open[i] = (HA_Open[i + 1] + HA_Close[i + 1]) / 2.0;

      // HA High = Max(High, HA Open, HA Close)
      HA_High[i] = MathMax(high, MathMax(HA_Open[i], HA_Close[i]));

      // HA Low = Min(Low, HA Open, HA Close)
      HA_Low[i] = MathMin(low, MathMin(HA_Open[i], HA_Close[i]));
   }
}

//+------------------------------------------------------------------+
//| Calculate ATR-based Trailing Stop                               |
//+------------------------------------------------------------------+
void CalculateTrailingStop()
{
   ArrayResize(TrailingStop, 50);

   for(int i = 0; i < 50; i++)
   {
      double hl2 = (iHigh(_Symbol, _Period, i) + iLow(_Symbol, _Period, i)) / 2.0;
      double atr_value = ATR[i] * KeyValue;

      // Basic trailing stop calculation
      double upper_band = hl2 + atr_value;
      double lower_band = hl2 - atr_value;

      // Determine trend direction and set trailing stop
      double close_price = UseHeikinAshi ? HA_Close[i] : iClose(_Symbol, _Period, i);

      if(i == 0)
      {
         if(close_price > upper_band)
            TrailingStop[i] = lower_band;
         else if(close_price < lower_band)
            TrailingStop[i] = upper_band;
         else
            TrailingStop[i] = TrailingStop[1]; // Previous value
      }
      else
      {
         // Trend continuation logic
         double prev_close = UseHeikinAshi ? HA_Close[i + 1] : iClose(_Symbol, _Period, i + 1);

         if(close_price > TrailingStop[i + 1] && prev_close > TrailingStop[i + 1])
            TrailingStop[i] = MathMax(lower_band, TrailingStop[i + 1]);
         else if(close_price < TrailingStop[i + 1] && prev_close < TrailingStop[i + 1])
            TrailingStop[i] = MathMin(upper_band, TrailingStop[i + 1]);
         else if(close_price > TrailingStop[i + 1])
            TrailingStop[i] = lower_band;
         else
            TrailingStop[i] = upper_band;
      }
   }
}

//+------------------------------------------------------------------+
//| Calculate Supertrend                                            |
//+------------------------------------------------------------------+
void CalculateSupertrend()
{
   ArrayResize(Supertrend, 50);

   for(int i = 0; i < 50; i++)
   {
      double hl2 = (iHigh(_Symbol, _Period, i) + iLow(_Symbol, _Period, i)) / 2.0;
      double atr_value = ATR[i] * SupertrendMulti;

      double upper_band = hl2 + atr_value;
      double lower_band = hl2 - atr_value;

      double close_price = iClose(_Symbol, _Period, i);

      if(i == 0)
      {
         Supertrend[i] = close_price > hl2 ? lower_band : upper_band;
      }
      else
      {
         double prev_close = iClose(_Symbol, _Period, i + 1);

         if(close_price > upper_band)
            Supertrend[i] = lower_band;
         else if(close_price < lower_band)
            Supertrend[i] = upper_band;
         else
            Supertrend[i] = Supertrend[i + 1];
      }
   }
}

//+------------------------------------------------------------------+
//| Detect Order Blocks                                             |
//+------------------------------------------------------------------+
void DetectOrderBlocks()
{
   ArrayResize(BullishOB_High, OB_LookBack);
   ArrayResize(BullishOB_Low, OB_LookBack);
   ArrayResize(BearishOB_High, OB_LookBack);
   ArrayResize(BearishOB_Low, OB_LookBack);
   ArrayResize(OB_Time, OB_LookBack);

   for(int i = 2; i < OB_LookBack; i++)
   {
      // Get candle data
      double open = iOpen(_Symbol, _Period, i);
      double high = iHigh(_Symbol, _Period, i);
      double low = iLow(_Symbol, _Period, i);
      double close = iClose(_Symbol, _Period, i);
      double volume = (double)iVolume(_Symbol, _Period, i);

      // Previous candle
      double prev_open = iOpen(_Symbol, _Period, i + 1);
      double prev_high = iHigh(_Symbol, _Period, i + 1);
      double prev_low = iLow(_Symbol, _Period, i + 1);
      double prev_close = iClose(_Symbol, _Period, i + 1);

      // Next candle
      double next_open = iOpen(_Symbol, _Period, i - 1);
      double next_close = iClose(_Symbol, _Period, i - 1);

      // ATR filter
      double atr_filter = ATR[i] * OB_ATR_Filter;
      double candle_range = high - low;

      if(candle_range < atr_filter)
         continue;

      // Bullish Order Block detection
      bool is_bullish_ob = false;
      if(close > open && // Bullish candle
         close > prev_close && // Higher close than previous
         low < prev_low && // Lower low than previous
         next_close > close) // Next candle confirms
      {
         is_bullish_ob = true;
         BullishOB_High[i] = high;
         BullishOB_Low[i] = low;
         OB_Time[i] = iTime(_Symbol, _Period, i);
      }

      // Bearish Order Block detection
      bool is_bearish_ob = false;
      if(close < open && // Bearish candle
         close < prev_close && // Lower close than previous
         high > prev_high && // Higher high than previous
         next_close < close) // Next candle confirms
      {
         is_bearish_ob = true;
         BearishOB_High[i] = high;
         BearishOB_Low[i] = low;
         OB_Time[i] = iTime(_Symbol, _Period, i);
      }

      // SCOB (Single Candle Order Block) detection
      if(!is_bullish_ob && !is_bearish_ob)
      {
         // Bullish SCOB - Engulfing pattern
         if(close > open &&
            close > prev_open &&
            open < prev_close &&
            (close - open) > (prev_close - prev_open) * 1.5)
         {
            BullishOB_High[i] = high;
            BullishOB_Low[i] = open;
            OB_Time[i] = iTime(_Symbol, _Period, i);
         }

         // Bearish SCOB - Engulfing pattern
         if(close < open &&
            close < prev_open &&
            open > prev_close &&
            (open - close) > (prev_open - prev_close) * 1.5)
         {
            BearishOB_High[i] = open;
            BearishOB_Low[i] = low;
            OB_Time[i] = iTime(_Symbol, _Period, i);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Check Entry Signals                                             |
//+------------------------------------------------------------------+
void CheckEntrySignals()
{
   // Get current values
   double current_price = iClose(_Symbol, _Period, 0);
   double prev_price = iClose(_Symbol, _Period, 1);

   double ha_close = UseHeikinAshi ? HA_Close[0] : current_price;
   double prev_ha_close = UseHeikinAshi ? HA_Close[1] : prev_price;

   double trailing_stop = TrailingStop[0];
   double prev_trailing_stop = TrailingStop[1];

   double hull_ma = HullMA[0];

   // Check filters
   bool trend_filter = true;
   bool adx_filter = true;

   if(EnableFilters)
   {
      if(UseSupertrend)
      {
         double supertrend = Supertrend[0];
         trend_filter = (current_price > supertrend); // Bullish trend
      }

      if(UseADXFilter)
      {
         double adx_value = ADX[0];
         adx_filter = (adx_value > ADXThreshold); // Trending market
      }
   }

   // Buy Signal Conditions
   bool buy_signal = false;
   if(ha_close > trailing_stop && // HA close above trailing stop
      prev_ha_close <= prev_trailing_stop && // Previous HA close was below trailing stop (crossover)
      current_price > hull_ma && // Price above Hull MA
      trend_filter && adx_filter) // Filters passed
   {
      buy_signal = true;
   }

   // Sell Signal Conditions
   bool sell_signal = false;
   if(ha_close < trailing_stop && // HA close below trailing stop
      prev_ha_close >= prev_trailing_stop && // Previous HA close was above trailing stop (crossover)
      current_price < hull_ma && // Price below Hull MA
      trend_filter && adx_filter) // Filters passed
   {
      sell_signal = true;
   }

   // Execute trades
   if(buy_signal && !LastBuySignal && GetOpenPositions() < MaxTrades)
   {
      OpenBuyTrade();
      LastBuySignal = true;
      LastSellSignal = false;
      LastSignalTime = TimeCurrent();

      if(ShowAlerts)
         Alert("MELONA OB EA: BUY Signal at ", current_price);
   }

   if(sell_signal && !LastSellSignal && GetOpenPositions() < MaxTrades)
   {
      OpenSellTrade();
      LastSellSignal = true;
      LastBuySignal = false;
      LastSignalTime = TimeCurrent();

      if(ShowAlerts)
         Alert("MELONA OB EA: SELL Signal at ", current_price);
   }

   // Reset signals if opposite direction
   if(!buy_signal) LastBuySignal = false;
   if(!sell_signal) LastSellSignal = false;
}

//+------------------------------------------------------------------+
//| Open Buy Trade                                                  |
//+------------------------------------------------------------------+
void OpenBuyTrade()
{
   double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double atr_value = ATR[0];

   // Calculate Stop Loss
   double stop_loss = entry_price - (atr_value * SL_ATR_Multi);

   // Calculate Take Profits
   double tp1 = entry_price + (entry_price * TP1_Percent / 100.0);
   double tp2 = entry_price + (entry_price * TP2_Percent / 100.0);
   double tp3 = entry_price + (entry_price * TP3_Percent / 100.0);

   // Calculate lot size
   double lot_size = CalculateLotSize(entry_price, stop_loss);

   // Open main position with TP1
   if(trade.Buy(lot_size, _Symbol, entry_price, stop_loss, tp1, EA_Comment + "_TP1"))
   {
      Print("Buy trade opened: Entry=", entry_price, " SL=", stop_loss, " TP1=", tp1);

      // Open additional positions for TP2 and TP3 if lot size allows
      double partial_lot = lot_size / 3.0;
      if(partial_lot >= SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN))
      {
         trade.Buy(partial_lot, _Symbol, entry_price, stop_loss, tp2, EA_Comment + "_TP2");
         trade.Buy(partial_lot, _Symbol, entry_price, stop_loss, tp3, EA_Comment + "_TP3");
      }
   }
}

//+------------------------------------------------------------------+
//| Open Sell Trade                                                 |
//+------------------------------------------------------------------+
void OpenSellTrade()
{
   double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double atr_value = ATR[0];

   // Calculate Stop Loss
   double stop_loss = entry_price + (atr_value * SL_ATR_Multi);

   // Calculate Take Profits
   double tp1 = entry_price - (entry_price * TP1_Percent / 100.0);
   double tp2 = entry_price - (entry_price * TP2_Percent / 100.0);
   double tp3 = entry_price - (entry_price * TP3_Percent / 100.0);

   // Calculate lot size
   double lot_size = CalculateLotSize(entry_price, stop_loss);

   // Open main position with TP1
   if(trade.Sell(lot_size, _Symbol, entry_price, stop_loss, tp1, EA_Comment + "_TP1"))
   {
      Print("Sell trade opened: Entry=", entry_price, " SL=", stop_loss, " TP1=", tp1);

      // Open additional positions for TP2 and TP3 if lot size allows
      double partial_lot = lot_size / 3.0;
      if(partial_lot >= SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN))
      {
         trade.Sell(partial_lot, _Symbol, entry_price, stop_loss, tp2, EA_Comment + "_TP2");
         trade.Sell(partial_lot, _Symbol, entry_price, stop_loss, tp3, EA_Comment + "_TP3");
      }
   }
}

//+------------------------------------------------------------------+
//| Calculate Lot Size                                              |
//+------------------------------------------------------------------+
double CalculateLotSize(double entry_price, double stop_loss)
{
   double lot_size = LotSize;

   if(UseRiskPercent)
   {
      double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
      double risk_amount = account_balance * RiskPercent / 100.0;
      double pip_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
      double stop_distance = MathAbs(entry_price - stop_loss);

      if(stop_distance > 0 && pip_value > 0)
      {
         lot_size = risk_amount / (stop_distance / SymbolInfoDouble(_Symbol, SYMBOL_POINT) * pip_value);
      }
   }

   // Normalize lot size
   double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

   lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
   lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;

   return lot_size;
}

//+------------------------------------------------------------------+
//| Get number of open positions                                    |
//+------------------------------------------------------------------+
int GetOpenPositions()
{
   int count = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Symbol() == _Symbol && position.Magic() == MagicNumber)
            count++;
      }
   }
   return count;
}

//+------------------------------------------------------------------+
//| Manage existing trades                                          |
//+------------------------------------------------------------------+
void ManageTrades()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Symbol() == _Symbol && position.Magic() == MagicNumber)
         {
            // Update trailing stop if needed
            UpdateTrailingStop(position.Ticket());

            // Check for partial close conditions
            CheckPartialClose(position.Ticket());
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Update trailing stop for position                               |
//+------------------------------------------------------------------+
void UpdateTrailingStop(ulong ticket)
{
   if(!position.SelectByTicket(ticket))
      return;

   double current_price = position.Type() == POSITION_TYPE_BUY ?
                         SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                         SymbolInfoDouble(_Symbol, SYMBOL_ASK);

   double new_sl = 0;
   double current_sl = position.StopLoss();

   if(position.Type() == POSITION_TYPE_BUY)
   {
      new_sl = current_price - (ATR[0] * SL_ATR_Multi);
      if(new_sl > current_sl && new_sl < current_price)
      {
         trade.PositionModify(ticket, new_sl, position.TakeProfit());
      }
   }
   else if(position.Type() == POSITION_TYPE_SELL)
   {
      new_sl = current_price + (ATR[0] * SL_ATR_Multi);
      if(new_sl < current_sl && new_sl > current_price)
      {
         trade.PositionModify(ticket, new_sl, position.TakeProfit());
      }
   }
}

//+------------------------------------------------------------------+
//| Check for partial close conditions                              |
//+------------------------------------------------------------------+
void CheckPartialClose(ulong ticket)
{
   // Implementation for partial close logic
   // This can be expanded based on specific requirements
}

//+------------------------------------------------------------------+
//| Update visual elements on chart                                 |
//+------------------------------------------------------------------+
void UpdateVisuals()
{
   if(!DrawLines) return;

   // Draw Hull MA line
   DrawLine("MelonaOB_HullMA", HullMA[0], clrBlue, 2);

   // Draw Trailing Stop line
   DrawLine("MelonaOB_TrailingStop", TrailingStop[0], clrRed, 1);

   // Draw Supertrend if enabled
   if(UseSupertrend)
   {
      color st_color = iClose(_Symbol, _Period, 0) > Supertrend[0] ? clrGreen : clrRed;
      DrawLine("MelonaOB_Supertrend", Supertrend[0], st_color, 1);
   }

   // Draw Order Blocks
   if(ShowOrderBlocks)
      DrawOrderBlocks();

   // Draw current signal status
   DrawSignalInfo();
}

//+------------------------------------------------------------------+
//| Draw line on chart                                              |
//+------------------------------------------------------------------+
void DrawLine(string name, double price, color line_color, int width)
{
   ObjectDelete(0, name);
   ObjectCreate(0, name, OBJ_HLINE, 0, 0, price);
   ObjectSetInteger(0, name, OBJPROP_COLOR, line_color);
   ObjectSetInteger(0, name, OBJPROP_WIDTH, width);
   ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
}

//+------------------------------------------------------------------+
//| Draw Order Blocks on chart                                      |
//+------------------------------------------------------------------+
void DrawOrderBlocks()
{
   // Clean up old order blocks
   ObjectsDeleteAll(0, "MelonaOB_OB_");

   datetime current_time = TimeCurrent();

   for(int i = 0; i < OB_LookBack; i++)
   {
      // Draw Bullish Order Blocks
      if(BullishOB_High[i] > 0 && BullishOB_Low[i] > 0)
      {
         string obj_name = "MelonaOB_OB_Bull_" + IntegerToString(i);
         datetime start_time = OB_Time[i];
         datetime end_time = current_time + PeriodSeconds(_Period) * 10;

         ObjectCreate(0, obj_name, OBJ_RECTANGLE, 0, start_time, BullishOB_High[i], end_time, BullishOB_Low[i]);
         ObjectSetInteger(0, obj_name, OBJPROP_COLOR, clrGreen);
         ObjectSetInteger(0, obj_name, OBJPROP_FILL, true);
         ObjectSetInteger(0, obj_name, OBJPROP_BACK, true);
         ObjectSetInteger(0, obj_name, OBJPROP_STYLE, STYLE_SOLID);
         ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 1);
      }

      // Draw Bearish Order Blocks
      if(BearishOB_High[i] > 0 && BearishOB_Low[i] > 0)
      {
         string obj_name = "MelonaOB_OB_Bear_" + IntegerToString(i);
         datetime start_time = OB_Time[i];
         datetime end_time = current_time + PeriodSeconds(_Period) * 10;

         ObjectCreate(0, obj_name, OBJ_RECTANGLE, 0, start_time, BearishOB_High[i], end_time, BearishOB_Low[i]);
         ObjectSetInteger(0, obj_name, OBJPROP_COLOR, clrRed);
         ObjectSetInteger(0, obj_name, OBJPROP_FILL, true);
         ObjectSetInteger(0, obj_name, OBJPROP_BACK, true);
         ObjectSetInteger(0, obj_name, OBJPROP_STYLE, STYLE_SOLID);
         ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 1);
      }
   }
}

//+------------------------------------------------------------------+
//| Draw signal information                                          |
//+------------------------------------------------------------------+
void DrawSignalInfo()
{
   string info_text = "MELONA ORDER BLOCK EA\n";
   info_text += "Hull MA: " + DoubleToString(HullMA[0], _Digits) + "\n";
   info_text += "Trailing Stop: " + DoubleToString(TrailingStop[0], _Digits) + "\n";
   info_text += "ATR: " + DoubleToString(ATR[0], _Digits) + "\n";

   if(UseHeikinAshi)
      info_text += "HA Close: " + DoubleToString(HA_Close[0], _Digits) + "\n";

   if(UseADXFilter)
      info_text += "ADX: " + DoubleToString(ADX[0], 2) + "\n";

   info_text += "Open Positions: " + IntegerToString(GetOpenPositions()) + "\n";
   info_text += "Last Signal: " + (LastBuySignal ? "BUY" : (LastSellSignal ? "SELL" : "NONE"));

   ObjectDelete(0, "MelonaOB_Info");
   ObjectCreate(0, "MelonaOB_Info", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "MelonaOB_Info", OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, "MelonaOB_Info", OBJPROP_XDISTANCE, 10);
   ObjectSetInteger(0, "MelonaOB_Info", OBJPROP_YDISTANCE, 30);
   ObjectSetString(0, "MelonaOB_Info", OBJPROP_TEXT, info_text);
   ObjectSetInteger(0, "MelonaOB_Info", OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(0, "MelonaOB_Info", OBJPROP_FONTSIZE, 8);
}


