//+------------------------------------------------------------------+
//|                                    MelonaOrderBlockEA_Simple.mq5 |
//|                          MELONA ORDER BLOCK STRATEGY EA - Simple |
//|                                   Simplified version for testing |
//+------------------------------------------------------------------+
#property copyright "MELONA ORDER BLOCK STRATEGY (By 3Dots) - MT5 Simple"
#property link      ""
#property version   "1.00"
#property strict

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>

//--- Global objects
CTrade         trade;
CPositionInfo  position;

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
input group "=== Basic Settings ==="
input int                HullLength = 21;                // Hull MA Length
input int                ATRPeriod = 14;                 // ATR Period
input double             KeyValue = 2.0;                 // ATR Key Value Multiplier
input double             TP_Percent = 1.0;               // Take Profit Percentage
input double             SL_ATR_Multi = 1.5;             // Stop Loss ATR Multiplier
input double             LotSize = 0.1;                  // Lot Size
input bool               UseHeikinAshi = true;           // Use Heikin Ashi
input bool               ShowAlerts = true;              // Show Alerts

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
double HullMA[];
double TrailingStop[];
double ATR[];
double HA_Close[];

bool LastBuySignal = false;
bool LastSellSignal = false;
int MagicNumber = 123456;
string EA_Comment = "MelonaOB_Simple";

// Indicator handles
int atr_handle = INVALID_HANDLE;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   trade.SetExpertMagicNumber(MagicNumber);

   // Initialize arrays
   ArraySetAsSeries(HullMA, true);
   ArraySetAsSeries(TrailingStop, true);
   ArraySetAsSeries(ATR, true);
   ArraySetAsSeries(HA_Close, true);

   // Create ATR indicator handle
   atr_handle = iATR(_Symbol, _Period, ATRPeriod);
   if(atr_handle == INVALID_HANDLE)
   {
      Print("Error creating ATR indicator handle");
      return(INIT_FAILED);
   }

   Print("MELONA ORDER BLOCK EA (Simple) initialized successfully");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Release indicator handle
   if(atr_handle != INVALID_HANDLE)
      IndicatorRelease(atr_handle);

   Print("MELONA ORDER BLOCK EA (Simple) deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   static datetime lastBarTime = 0;
   datetime currentBarTime = iTime(_Symbol, _Period, 0);
   
   if(currentBarTime == lastBarTime)
      return;
   lastBarTime = currentBarTime;
   
   CalculateIndicators();
   CheckEntrySignals();
}

//+------------------------------------------------------------------+
//| Calculate indicators                                             |
//+------------------------------------------------------------------+
void CalculateIndicators()
{
   // Calculate ATR
   int atr_handle = iATR(_Symbol, _Period, ATRPeriod);
   if(CopyBuffer(atr_handle, 0, 0, 10, ATR) <= 0)
      return;
   
   // Calculate simple Hull MA (using WMA approximation)
   ArrayResize(HullMA, 10);
   for(int i = 0; i < 10; i++)
   {
      HullMA[i] = CalculateSimpleHMA(i, HullLength);
   }
   
   // Calculate Heikin Ashi if enabled
   if(UseHeikinAshi)
   {
      ArrayResize(HA_Close, 10);
      for(int i = 0; i < 10; i++)
      {
         double open = iOpen(_Symbol, _Period, i);
         double high = iHigh(_Symbol, _Period, i);
         double low = iLow(_Symbol, _Period, i);
         double close = iClose(_Symbol, _Period, i);
         HA_Close[i] = (open + high + low + close) / 4.0;
      }
   }
   
   // Calculate Trailing Stop
   ArrayResize(TrailingStop, 10);
   for(int i = 0; i < 10; i++)
   {
      double hl2 = (iHigh(_Symbol, _Period, i) + iLow(_Symbol, _Period, i)) / 2.0;
      double atr_value = ATR[i] * KeyValue;
      double close_price = UseHeikinAshi ? HA_Close[i] : iClose(_Symbol, _Period, i);
      
      if(close_price > hl2)
         TrailingStop[i] = hl2 - atr_value;
      else
         TrailingStop[i] = hl2 + atr_value;
   }
}

//+------------------------------------------------------------------+
//| Calculate simple Hull MA approximation                          |
//+------------------------------------------------------------------+
double CalculateSimpleHMA(int shift, int period)
{
   double wma_half = CalculateWMA(shift, period / 2) * 2;
   double wma_full = CalculateWMA(shift, period);
   return wma_half - wma_full;
}

//+------------------------------------------------------------------+
//| Calculate Weighted Moving Average                               |
//+------------------------------------------------------------------+
double CalculateWMA(int shift, int period)
{
   double sum = 0;
   double weight_sum = 0;
   
   for(int i = 0; i < period; i++)
   {
      double price = iClose(_Symbol, _Period, shift + i);
      double weight = period - i;
      sum += price * weight;
      weight_sum += weight;
   }
   
   return weight_sum > 0 ? sum / weight_sum : 0;
}

//+------------------------------------------------------------------+
//| Check Entry Signals                                             |
//+------------------------------------------------------------------+
void CheckEntrySignals()
{
   double current_price = iClose(_Symbol, _Period, 0);
   double prev_price = iClose(_Symbol, _Period, 1);
   
   double ha_close = UseHeikinAshi ? HA_Close[0] : current_price;
   double prev_ha_close = UseHeikinAshi ? HA_Close[1] : prev_price;
   
   double trailing_stop = TrailingStop[0];
   double prev_trailing_stop = TrailingStop[1];
   double hull_ma = HullMA[0];
   
   // Buy Signal
   bool buy_signal = false;
   if(ha_close > trailing_stop && 
      prev_ha_close <= prev_trailing_stop && 
      current_price > hull_ma)
   {
      buy_signal = true;
   }
   
   // Sell Signal  
   bool sell_signal = false;
   if(ha_close < trailing_stop && 
      prev_ha_close >= prev_trailing_stop && 
      current_price < hull_ma)
   {
      sell_signal = true;
   }
   
   // Execute trades
   if(buy_signal && !LastBuySignal && GetOpenPositions() == 0)
   {
      OpenBuyTrade();
      LastBuySignal = true;
      LastSellSignal = false;
      
      if(ShowAlerts)
         Alert("MELONA OB EA: BUY Signal at ", current_price);
   }
   
   if(sell_signal && !LastSellSignal && GetOpenPositions() == 0)
   {
      OpenSellTrade();
      LastSellSignal = true;
      LastBuySignal = false;
      
      if(ShowAlerts)
         Alert("MELONA OB EA: SELL Signal at ", current_price);
   }
   
   if(!buy_signal) LastBuySignal = false;
   if(!sell_signal) LastSellSignal = false;
}

//+------------------------------------------------------------------+
//| Open Buy Trade                                                  |
//+------------------------------------------------------------------+
void OpenBuyTrade()
{
   double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double atr_value = ATR[0];
   
   double stop_loss = entry_price - (atr_value * SL_ATR_Multi);
   double take_profit = entry_price + (entry_price * TP_Percent / 100.0);
   
   if(trade.Buy(LotSize, _Symbol, entry_price, stop_loss, take_profit, EA_Comment))
   {
      Print("Buy trade opened: Entry=", entry_price, " SL=", stop_loss, " TP=", take_profit);
   }
}

//+------------------------------------------------------------------+
//| Open Sell Trade                                                 |
//+------------------------------------------------------------------+
void OpenSellTrade()
{
   double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double atr_value = ATR[0];
   
   double stop_loss = entry_price + (atr_value * SL_ATR_Multi);
   double take_profit = entry_price - (entry_price * TP_Percent / 100.0);
   
   if(trade.Sell(LotSize, _Symbol, entry_price, stop_loss, take_profit, EA_Comment))
   {
      Print("Sell trade opened: Entry=", entry_price, " SL=", stop_loss, " TP=", take_profit);
   }
}

//+------------------------------------------------------------------+
//| Get number of open positions                                    |
//+------------------------------------------------------------------+
int GetOpenPositions()
{
   int count = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Symbol() == _Symbol && position.Magic() == MagicNumber)
            count++;
      }
   }
   return count;
}
